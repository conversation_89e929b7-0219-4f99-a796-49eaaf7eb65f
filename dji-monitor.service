[Unit]
Description=DJI Device Monitor Service
Documentation=DJI设备监控服务（多云存储备份增强版）
After=network.target
Wants=network.target

[Service]
Type=simple
User=root
Group=root
ExecStart=/usr/bin/python3 /opt/dji_monitor/dji_monitor.py
WorkingDirectory=/opt/dji_monitor
Restart=always
RestartSec=10
StandardOutput=journal
StandardError=journal
SyslogIdentifier=dji-monitor

# 环境变量
Environment=PYTHONPATH=/opt/dji_monitor
Environment=PYTHONUNBUFFERED=1

# 安全设置 - 加强安全限制
NoNewPrivileges=true
PrivateTmp=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/opt/dji_monitor /var/log /tmp /media
ProtectKernelTunables=true
ProtectKernelModules=true
ProtectControlGroups=true
RestrictSUIDSGID=true
RemoveIPC=true
RestrictRealtime=true
RestrictNamespaces=true
LockPersonality=true
MemoryDenyWriteExecute=true
SystemCallFilter=@system-service
SystemCallFilter=~@debug @mount @module @obsolete @reboot @swap

# 资源限制
LimitNOFILE=65536
TimeoutStartSec=60
TimeoutStopSec=30

[Install]
WantedBy=multi-user.target
