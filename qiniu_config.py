# -*- coding: utf-8 -*-
"""
七牛云存储配置文件
"""

# 七牛云存储配置
QINIU_ACCESS_KEY = '4I_GtwMsXHsNaW9V4QtvLprssoZE7Z4ZGx3kXsJP'
QINIU_SECRET_KEY = 'F8_p1x1g6sTdVyvj01m7e3IKbc7JqA9fNIM9_3dg'
QINIU_BUCKET_NAME = 'wurenjidd'

# 上传配置
QINIU_UPLOAD_ENABLED = True  # 是否启用七牛云上传
QINIU_UPLOAD_TIMEOUT = 36000  # 上传超时时间（秒）
QINIU_TOKEN_EXPIRE = 3600    # Token过期时间（秒）

# 上传策略配置
QINIU_UPLOAD_POLICY = {
    # 可以在这里添加自定义的上传策略
    # 例如：回调URL、文件大小限制等
}

# 文件命名策略
QINIU_FILE_PREFIX = 'dji_backup'  # 文件前缀
QINIU_USE_TIMESTAMP = True        # 是否在文件名中包含时间戳
QINIU_PRESERVE_PATH = True        # 是否保持原始目录结构

# 上传重试配置
QINIU_MAX_RETRIES = 3            # 最大重试次数
QINIU_RETRY_DELAY = 5            # 重试间隔（秒）

# 本地文件管理配置
QINIU_DELETE_AFTER_UPLOAD = True  # 上传成功后是否删除本地文件
QINIU_WAIT_FOR_UPLOAD = True      # 是否等待上传完成再继续后续操作

# 并发上传配置
QINIU_MAX_UPLOAD_WORKERS = 5     # 最大并发上传线程数
QINIU_CHUNK_SIZE = 4 * 1024 * 1024  # 分片上传大小（4MB）

# 日志配置
QINIU_LOG_LEVEL = 'INFO'         # 日志级别
QINIU_LOG_UPLOADS = True         # 是否记录上传详情

# 存储区域配置（可选）
# 华东: 'z0', 华北: 'z1', 华南: 'z2', 北美: 'na0', 东南亚: 'as0'
QINIU_ZONE = None  # 自动选择最优区域

# 设备状态上报API配置
DEVICE_STATUS_API_ENABLED = True  # 是否启用设备状态上报
DEVICE_STATUS_API_URL = "http://localhost:8080/api/device/status"  # 设备状态上报接口URL（测试用）
# DEVICE_STATUS_API_URL = "http://your-api-server.com/api/device/status"  # 生产环境URL
DEVICE_STATUS_API_TIMEOUT = 10  # API请求超时时间（秒）
DEVICE_STATUS_API_RETRIES = 3   # API请求重试次数
DEVICE_STATUS_API_ASYNC = True  # 是否异步发送API请求（推荐开启）

# API请求头配置
DEVICE_STATUS_API_HEADERS = {
    "Content-Type": "application/json",
    "User-Agent": "DJI-Monitor-Service/1.0",
    # "Authorization": "Bearer your-token-here",  # 如果需要认证，取消注释并设置token
}

# 设备状态事件类型
DEVICE_EVENT_CONNECTED = "connected"     # 设备连接事件
DEVICE_EVENT_DISCONNECTED = "disconnected"  # 设备断开事件

