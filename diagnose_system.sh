#!/bin/bash

# 设置脚本权限
chmod +x "$0" 2>/dev/null || true

# DJI监控服务系统诊断脚本
# 用于诊断和修复sudo权限问题及磁盘问题

echo "=== DJI监控服务系统诊断工具 ==="
echo ""

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "警告: 建议以root权限运行此脚本以获得完整诊断信息"
    echo "使用: sudo $0"
    echo ""
fi

echo "1. 系统基本信息检查..."
echo "操作系统: $(lsb_release -d 2>/dev/null | cut -f2 || echo "未知")"
echo "内核版本: $(uname -r)"
echo "当前用户: $(whoami)"
echo "当前UID: $(id -u)"
echo ""

echo "2. 磁盘空间检查..."
echo "磁盘使用情况:"
df -h
echo ""

echo "可用内存:"
free -h
echo ""

echo "3. sudo配置检查..."
if [ -f "/etc/sudoers" ]; then
    echo "sudoers文件存在"
    
    # 检查sudo二进制文件
    if [ -f "/usr/bin/sudo" ]; then
        echo "sudo二进制文件: /usr/bin/sudo"
        ls -la /usr/bin/sudo
        
        # 检查sudo权限位
        if [ -u "/usr/bin/sudo" ]; then
            echo "✓ sudo具有setuid权限"
        else
            echo "✗ sudo缺少setuid权限 - 这可能是问题所在!"
            if [ "$EUID" -eq 0 ]; then
                echo "尝试修复sudo权限..."
                chmod u+s /usr/bin/sudo
                echo "sudo权限已修复"
            else
                echo "需要root权限来修复sudo"
            fi
        fi
    else
        echo "✗ sudo二进制文件不存在"
    fi
else
    echo "✗ sudoers文件不存在"
fi
echo ""

echo "4. 文件系统检查..."
echo "挂载点信息:"
mount | grep -E "(ext[234]|xfs|btrfs)" | head -10
echo ""

echo "检查文件系统错误..."
if [ "$EUID" -eq 0 ]; then
    # 检查根分区
    root_device=$(df / | awk 'NR==2 {print $1}')
    echo "根分区设备: $root_device"
    
    # 只读检查文件系统
    echo "执行文件系统只读检查..."
    fsck -n "$root_device" 2>/dev/null | head -10 || echo "无法检查文件系统"
else
    echo "需要root权限来检查文件系统"
fi
echo ""

echo "5. DJI监控服务状态检查..."
if systemctl list-units --full -all | grep -Fq "dji-monitor.service"; then
    echo "DJI监控服务状态:"
    systemctl status dji-monitor.service --no-pager -l
else
    echo "DJI监控服务未安装"
fi
echo ""

echo "6. 进程和挂载点检查..."
echo "DJI相关进程:"
ps aux | grep -i dji | grep -v grep || echo "无DJI相关进程"
echo ""

echo "DJI相关挂载点:"
mount | grep -i dji || echo "无DJI相关挂载点"
echo ""

echo "临时挂载记录:"
if [ -f "/tmp/dji_mounts.log" ]; then
    cat /tmp/dji_mounts.log
else
    echo "无临时挂载记录"
fi
echo ""

echo "7. 权限问题诊断..."
if [ "$EUID" -ne 0 ]; then
    echo "测试sudo权限..."
    if sudo -n true 2>/dev/null; then
        echo "✓ sudo权限正常"
    else
        echo "✗ sudo权限异常"
        echo "可能的原因:"
        echo "  1. sudo二进制文件权限不正确"
        echo "  2. 文件系统以nosuid选项挂载"
        echo "  3. sudoers配置问题"
        echo "  4. 用户不在sudo组中"
        
        echo ""
        echo "建议的修复步骤:"
        echo "  1. 重启系统"
        echo "  2. 以root用户登录"
        echo "  3. 运行: chmod u+s /usr/bin/sudo"
        echo "  4. 检查挂载选项: mount | grep nosuid"
        echo "  5. 将用户添加到sudo组: usermod -aG sudo \$USER"
    fi
fi
echo ""

echo "8. 系统日志检查..."
echo "最近的系统错误:"
if [ "$EUID" -eq 0 ]; then
    journalctl --since "1 hour ago" --priority=err --no-pager | tail -10 || echo "无法读取系统日志"
else
    echo "需要root权限来读取系统日志"
fi
echo ""

echo "=== 诊断完成 ==="
echo ""
echo "如果发现问题，建议的修复步骤:"
echo "1. 如果sudo权限异常，请重启系统或以root用户修复"
echo "2. 如果磁盘空间不足，请清理不必要的文件"
echo "3. 如果文件系统有错误，请在单用户模式下运行fsck"
echo "4. 如果问题持续存在，请联系系统管理员"
