"""
测试MQTT服务功能
"""

import json
import time
from mqtt_api_service import EMQXMQTTService


def test_mqtt_service():
    """测试MQTT服务的各种功能"""
    print("=== EMQX MQTT API 服务测试 ===\n")
    
    # 创建服务实例
    mqtt_service = EMQXMQTTService()
    
    # 1. 测试获取客户端列表
    print("1. 测试获取客户端列表...")
    result = mqtt_service.get_clients()
    if result['success']:
        clients_data = result['data']
        if isinstance(clients_data, dict) and 'data' in clients_data:
            clients_list = clients_data['data']
        elif isinstance(clients_data, list):
            clients_list = clients_data
        else:
            clients_list = []

        print(f"✓ 获取客户端列表成功，共 {len(clients_list)} 个客户端")
        if clients_list:
            print(f"  第一个客户端ID: {clients_list[0].get('clientid', 'N/A')}")
    else:
        print(f"✗ 获取客户端列表失败: {result['error']}")
    print()
    
    # 2. 测试设备推送给平台
    print("2. 设备推送给平台...")
    result = mqtt_service.send_uav_command('uav_upload', '867085072381182', '1', 'DJI-1581F87LC252Q002168Y', 'https://www.baidu.com')
    if result['success']:
        print("✓ 开门命令发送成功")
        print(f"  消息ID: {result['data'].get('id', 'N/A')}")
    else:
        print(f"✗ 开门命令发送失败: {result['error']}")
    print()
    
    # 等待一下
    time.sleep(2)
    
    # # 3. 测试发布拔出命令
    print("3. 测试发布拔出命令...")
    result = mqtt_service.send_uav_command('uav_leave', '867085072381182', '1', 'DJI-1581F87LC252Q002168Y')
    if result['success']:
        print("✓ 开门命令发送成功")
        print(f"  消息ID: {result['data'].get('id', 'N/A')}")
    else:
        print(f"✗ 开门命令发送失败: {result['error']}")
    print()
    
    # 等待一下
    time.sleep(2)

    # # 4. 测试发布插入命令
    print("4. 测试发布插入命令...")
    result = mqtt_service.send_uav_command('uav_insert', '867085072381182', '1', 'DJI-1581F87LC252Q002168Y')
    if result['success']:
        print("✓ 开门命令发送成功")
        print(f"  消息ID: {result['data'].get('id', 'N/A')}")
    else:
        print(f"✗ 开门命令发送失败: {result['error']}")
    print()
    
    # 等待一下
    time.sleep(2)
    
    # 5. 测试发布自定义消息
    print("5. 测试发布自定义消息...")
    custom_payload = {
        "type": "uav_upload",
        "deviceId": "867085072381182",
        "doorNo": "1",
        "uavNo": "DJI-1581F87LC252Q002168Y",
        "urls": "https://www.baidu.com"
    }
    result = mqtt_service.publish_message('/device/uav/867085072381182/notify', custom_payload, 1)
    if result['success']:
        print("✓ 自定义消息发送成功")
        print(f"  消息ID: {result['data'].get('id', 'N/A')}")
    else:
        print(f"✗ 自定义消息发送失败: {result['error']}")
    print()
    
    # 5. 测试创建订阅（如果有客户端的话）
    # print("5. 测试创建订阅...")
    # test_clientid = "test_client_001"  # 使用一个测试客户端ID
    # result = mqtt_service.subscribe_to_device_data(test_clientid)
    # if result['success']:
    #     print(f"✓ 为客户端 {test_clientid} 创建订阅成功")
    # else:
    #     print(f"✗ 为客户端 {test_clientid} 创建订阅失败: {result['error']}")
    #     print("  注意: 如果客户端不存在，这是正常的")
    # print()
    
    # print("=== 测试完成 ===")


if __name__ == '__main__':
    test_mqtt_service()
