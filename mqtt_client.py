#!/usr/bin/env python3
"""
MQTT客户端模块
用于DJI设备状态上报的MQTT消息发送
"""

import json
import base64
import urllib.request
import urllib.error
import logging
import time
from typing import Dict, Any, Optional


# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# EMQX 服务器配置
EMQX_CONFIG = {
    'host': '**************',
    'port': 18083,
    'username': '467bd2b376974c4f',
    'password': 'D50n0U6dwIzgaOC7V4FyjZzdWcORzPgigQdGXlYxYdE',
    'base_url': 'http://**************:18083/api/v5'
}

# MQTT 主题配置
MQTT_TOPICS = {
    'device_send': '/device/uav/867085072381182/notify',  # 设备发送数据的主题
    'device_accept': '/device/uav/867085072381182/notify',  # 设备接收命令的主题
    'qos': 0  # QoS 级别
}

# 设备控制命令模板
DEVICE_COMMANDS = {
    'uav_upload': {
        "type": "uav_upload",
        "deviceId": "867085072381182",
        "doorNo": "1",
        "uavNo": "DJI-1581F87LC252Q002168Y",
        "urls": "https://www.baidu.com,https://www.baidu.com"
    },
    'uav_leave': {
        "type": "uav_leave",
        "deviceId": "867085072381182",
        "doorNo": "1",
        "uavNo": "DJI-1581F87LC252Q002168Y"
    },
    'uav_insert': {
        "type": "uav_insert",
        "deviceId": "867085072381182",
        "doorNo": "1",
        "uavNo": "DJI-1581F87LC252Q002168Y"
    }
}



class EMQXMQTTService:
    """EMQX MQTT API 服务类"""
    
    def __init__(self):
        self.base_url = EMQX_CONFIG['base_url']
        self.username = EMQX_CONFIG['username']
        self.password = EMQX_CONFIG['password']
        self.auth_header = self._create_auth_header()
    
    def _create_auth_header(self) -> str:
        """创建认证头"""
        credentials = f"{self.username}:{self.password}"
        encoded_credentials = base64.b64encode(credentials.encode()).decode()
        return f"Basic {encoded_credentials}"
    
    def _make_request(self, endpoint: str, method: str = 'GET', data: Optional[Dict] = None) -> Dict[str, Any]:
        """发送HTTP请求到EMQX API"""
        url = f"{self.base_url}/{endpoint}"
        
        # 准备请求数据
        request_data = None
        if data:
            request_data = json.dumps(data).encode('utf-8')
        
        # 创建请求
        req = urllib.request.Request(url, data=request_data, method=method)
        req.add_header('Content-Type', 'application/json')
        req.add_header('Authorization', self.auth_header)
        
        try:
            with urllib.request.urlopen(req) as response:
                response_data = json.loads(response.read().decode())
                logger.info(f"API请求成功: {method} {endpoint}")
                return {
                    'success': True,
                    'data': response_data,
                    'status_code': response.status
                }
        except urllib.error.HTTPError as e:
            error_data = {}
            try:
                error_data = json.loads(e.read().decode())
            except:
                error_data = {'message': str(e)}
            
            logger.error(f"API请求失败: {method} {endpoint}, 错误: {error_data}")
            return {
                'success': False,
                'error': error_data,
                'status_code': e.code
            }
        except Exception as e:
            logger.error(f"请求异常: {str(e)}")
            return {
                'success': False,
                'error': {'message': str(e)},
                'status_code': 500
            }
    
    def publish_message(self, topic: str, payload: Dict[str, Any], qos: int = 1) -> Dict[str, Any]:
        """发布MQTT消息"""
        publish_data = {
            'topic': topic,
            'payload': json.dumps(payload),
            'qos': qos,
            'retain': False
        }
        print(publish_data)
        result = self._make_request('publish', 'POST', publish_data)
        
        if result['success']:
            logger.info(f"消息发布成功到主题: {topic}")
        else:
            logger.error(f"消息发布失败到主题: {topic}")
        
        return result
    
    def create_subscription(self, clientid: str, topic: str, qos: int = 1) -> Dict[str, Any]:
        """为客户端创建订阅"""
        subscription_data = {
            'topic': topic,
            'qos': qos
        }
        
        endpoint = f"clients/{clientid}/subscribe"
        result = self._make_request(endpoint, 'POST', subscription_data)
        
        if result['success']:
            logger.info(f"订阅创建成功: 客户端 {clientid} 订阅主题 {topic}")
        else:
            logger.error(f"订阅创建失败: 客户端 {clientid} 订阅主题 {topic}")
        
        return result
    
    def get_subscriptions(self, clientid: str) -> Dict[str, Any]:
        """获取客户端的订阅列表"""
        endpoint = f"clients/{clientid}/subscriptions"
        result = self._make_request(endpoint, 'GET')
        
        if result['success']:
            logger.info(f"获取订阅列表成功: 客户端 {clientid}")
        else:
            logger.error(f"获取订阅列表失败: 客户端 {clientid}")
        
        return result
    
    def unsubscribe(self, clientid: str, topic: str) -> Dict[str, Any]:
        """取消客户端订阅"""
        unsubscribe_data = {
            'topic': topic
        }
        
        endpoint = f"clients/{clientid}/unsubscribe"
        result = self._make_request(endpoint, 'POST', unsubscribe_data)
        
        if result['success']:
            logger.info(f"取消订阅成功: 客户端 {clientid} 取消订阅主题 {topic}")
        else:
            logger.error(f"取消订阅失败: 客户端 {clientid} 取消订阅主题 {topic}")
        
        return result
    
    def get_clients(self) -> Dict[str, Any]:
        """获取连接的客户端列表"""
        result = self._make_request('clients', 'GET')
        
        if result['success']:
            logger.info("获取客户端列表成功")
        else:
            logger.error("获取客户端列表失败")
        
        return result
    
    def get_client_info(self, clientid: str) -> Dict[str, Any]:
        """获取特定客户端信息"""
        endpoint = f"clients/{clientid}"
        result = self._make_request(endpoint, 'GET')
        
        if result['success']:
            logger.info(f"获取客户端信息成功: {clientid}")
        else:
            logger.error(f"获取客户端信息失败: {clientid}")
        
        return result
    


    def send_uav_command(self, action: str, deviceId: str, doorNo: str, uavNo: str, urls: str = None) -> Dict[str, Any]:
        """发送UAV控制命令"""
        if action not in DEVICE_COMMANDS:
            return {
                'success': False,
                'error': {'message': f'不支持的操作: {action}'},
                'status_code': 400
            }
        command = DEVICE_COMMANDS[action].copy()
        command['deviceId'] = deviceId
        command['doorNo'] = doorNo
        command['uavNo'] = uavNo
        if urls:
            command['urls'] = urls
        topic = "/device/uav/"+deviceId+"/notify"
        qos = MQTT_TOPICS['qos']
        
        return self.publish_message(topic, command, qos)

    
    def subscribe_to_device_data(self, clientid: str) -> Dict[str, Any]:
        """订阅设备数据主题"""
        topic = MQTT_TOPICS['device_send']
        qos = MQTT_TOPICS['qos']

        return self.create_subscription(clientid, topic, qos)

  

# class MQTTClient:
#     """MQTT客户端封装类"""
    
#     def __init__(self, config: Optional[Dict[str, Any]] = None):
#         self.config = config or MQTT_CONFIG
#         self.client = None
#         self.connected = False
#         self.logger = logging.getLogger(__name__)
        
#         if not MQTT_AVAILABLE:
#             self.logger.warning("MQTT客户端库不可用，将使用日志模式")
#             return
            
#         self._init_client()
    
#     def _init_client(self):
#         """初始化MQTT客户端"""
#         try:
#             self.client = mqtt.Client(
#                 client_id=self.config['client_id'],
#                 clean_session=True
#             )
            
#             # 设置回调函数
#             self.client.on_connect = self._on_connect
#             self.client.on_disconnect = self._on_disconnect
#             self.client.on_publish = self._on_publish
            
#             # 设置认证信息（如果需要）
#             if self.config.get('username') and self.config.get('password'):
#                 self.client.username_pw_set(
#                     self.config['username'], 
#                     self.config['password']
#                 )
                
#         except Exception as e:
#             self.logger.error(f"初始化MQTT客户端失败: {e}")
    
#     def _on_connect(self, client, userdata, flags, rc):
#         """连接回调"""
#         if rc == 0:
#             self.connected = True
#             self.logger.info("MQTT连接成功")
#         else:
#             self.connected = False
#             self.logger.error(f"MQTT连接失败，返回码: {rc}")
    
#     def _on_disconnect(self, client, userdata, rc):
#         """断开连接回调"""
#         self.connected = False
#         if rc != 0:
#             self.logger.warning(f"MQTT连接意外断开，返回码: {rc}")
#         else:
#             self.logger.info("MQTT连接已断开")
    
#     def _on_publish(self, client, userdata, mid):
#         """发布消息回调"""
#         self.logger.debug(f"MQTT消息发布成功，消息ID: {mid}")
    
#     def connect(self) -> bool:
#         """连接到MQTT服务器"""
#         if not MQTT_AVAILABLE or not self.client:
#             return False
            
#         try:
#             self.client.connect(
#                 self.config['broker'],
#                 self.config['port'],
#                 self.config['keepalive']
#             )
#             self.client.loop_start()
            
#             # 等待连接建立
#             timeout = self.config['timeout']
#             start_time = time.time()
#             while not self.connected and (time.time() - start_time) < timeout:
#                 time.sleep(0.1)
                
#             return self.connected
            
#         except Exception as e:
#             self.logger.error(f"MQTT连接异常: {e}")
#             return False
    
#     def disconnect(self):
#         """断开MQTT连接"""
#         if self.client and self.connected:
#             try:
#                 self.client.loop_stop()
#                 self.client.disconnect()
#                 self.connected = False
#                 self.logger.info("MQTT连接已断开")
#             except Exception as e:
#                 self.logger.error(f"断开MQTT连接异常: {e}")
    
#     def publish(self, message: Dict[str, Any]) -> bool:
#         """发布消息"""
#         if not MQTT_AVAILABLE or not self.client or not self.connected:
#             self.logger.warning("MQTT不可用或未连接，消息将只记录到日志")
#             return False
            
#         try:
#             # 将消息转换为JSON字符串
#             if isinstance(message, dict):
#                 payload = json.dumps(message, ensure_ascii=False)
#             else:
#                 payload = str(message)
            
#             # 发布消息
#             result = self.client.publish(
#                 self.config['topic'],
#                 payload,
#                 qos=1  # 至少一次传递
#             )
            
#             if result.rc == mqtt.MQTT_ERR_SUCCESS:
#                 self.logger.info(f"MQTT消息发布成功: {payload}")
#                 return True
#             else:
#                 self.logger.error(f"MQTT消息发布失败，返回码: {result.rc}")
#                 return False
                
#         except Exception as e:
#             self.logger.error(f"MQTT消息发布异常: {e}")
#             return False

