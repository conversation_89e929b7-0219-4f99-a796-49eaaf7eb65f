#!/bin/bash

echo "🔧 修复DJI监控服务挂载问题"
echo "================================"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 错误: 此脚本需要root权限"
    echo "请使用: sudo $0"
    exit 1
fi

echo "✅ Root权限检查通过"

# 1. 停止服务
echo ""
echo "🛑 停止DJI监控服务..."
systemctl stop dji-monitor

# 2. 清理残留挂载点
echo ""
echo "🧹 清理残留挂载点..."
for mount_point in $(mount | grep "/media/dji_device_" | awk '{print $3}'); do
    echo "  卸载: $mount_point"
    umount "$mount_point" 2>/dev/null || umount -f "$mount_point" 2>/dev/null || true
    rmdir "$mount_point" 2>/dev/null || true
done

# 3. 更新systemd服务文件
echo ""
echo "📋 更新systemd服务配置..."
cp dji-monitor.service /etc/systemd/system/
chmod 644 /etc/systemd/system/dji-monitor.service

# 4. 更新Python代码
echo ""
echo "🐍 更新Python代码..."
cp dji_monitor.py /opt/dji_monitor/
cp secure_config.py /opt/dji_monitor/
chmod 644 /opt/dji_monitor/*.py

# 5. 重新加载systemd
echo ""
echo "🔄 重新加载systemd..."
systemctl daemon-reload

# 6. 验证服务配置
echo ""
echo "🔍 验证服务配置..."
if systemd-analyze verify /etc/systemd/system/dji-monitor.service; then
    echo "✅ systemd服务配置验证通过"
else
    echo "❌ systemd服务配置有问题"
    exit 1
fi

# 7. 检查设备分区
echo ""
echo "💾 检查当前USB设备..."
lsblk -o NAME,SIZE,TYPE,MOUNTPOINT | grep -E "(disk|part)"

# 8. 启动服务
echo ""
echo "🚀 启动DJI监控服务..."
systemctl start dji-monitor

# 等待服务启动
sleep 3

# 9. 检查服务状态
echo ""
echo "📊 检查服务状态..."
if systemctl is-active --quiet dji-monitor; then
    echo "✅ DJI监控服务启动成功!"
    echo ""
    echo "📝 实时日志:"
    echo "   journalctl -u dji-monitor -f"
    echo ""
    echo "🔍 最近的日志:"
    journalctl -u dji-monitor --no-pager -l | tail -10
else
    echo "❌ 服务启动失败"
    echo ""
    echo "🔍 错误信息:"
    journalctl -u dji-monitor --no-pager -l | tail -20
    exit 1
fi

echo ""
echo "🎉 修复完成!"
echo ""
echo "📚 主要修复内容:"
echo "   ✅ 修复了systemd SystemCallFilter配置"
echo "   ✅ 添加了设备分区检测逻辑"
echo "   ✅ 改进了挂载策略"
echo "   ✅ 清理了残留挂载点"
echo ""
echo "🔧 如果仍有问题，请检查:"
echo "   1. 设备是否正确连接: lsblk"
echo "   2. 设备权限: ls -la /dev/sd*"
echo "   3. 服务日志: journalctl -u dji-monitor -f"
