#!/usr/bin/env python3
"""
DJI监控服务安全配置文件
包含所有必要的配置常量和安全设置
"""

import os
import logging

# 基础安全配置
SECURITY_CONFIG = {
    'max_mount_points': 10,  # 最大同时挂载点数量
    'allowed_mount_base': '/media',  # 允许的挂载基础路径
    'allowed_backup_bases': ['/home', '/opt/dji_backup'],  # 允许的备份基础路径
    'max_file_size': 10 * 1024 * 1024 * 1024,  # 10GB 单文件最大大小
    'max_total_size': 100 * 1024 * 1024 * 1024,  # 100GB 总备份大小限制
    'command_timeout': 300,  # 命令执行超时时间（秒）
    'enable_shell_commands': False,  # 是否启用shell命令（安全起见默认关闭）
}

# 云存储配置（默认值）
CLOUD_STORAGE_PROVIDER = 'qiniu'
CLOUD_UPLOAD_ENABLED = False  # 默认关闭云上传

# 七牛云配置（如果启用）
QINIU_ACCESS_KEY = os.getenv('QINIU_ACCESS_KEY', '')
QINIU_SECRET_KEY = os.getenv('QINIU_SECRET_KEY', '')
QINIU_BUCKET = os.getenv('QINIU_BUCKET', 'dji-backup')
QINIU_DOMAIN = os.getenv('QINIU_DOMAIN', '')
QINIU_UPLOAD_ENABLED = bool(QINIU_ACCESS_KEY and QINIU_SECRET_KEY)
QINIU_UPLOAD_TIMEOUT = 3600  # 1小时
QINIU_MAX_RETRIES = 3
QINIU_RETRY_DELAY = 5  # 5秒

# 阿里云OSS配置（如果启用）
ALIYUN_ACCESS_KEY_ID = os.getenv('ALIYUN_ACCESS_KEY_ID', '')
ALIYUN_ACCESS_KEY_SECRET = os.getenv('ALIYUN_ACCESS_KEY_SECRET', '')
ALIYUN_BUCKET = os.getenv('ALIYUN_BUCKET', 'dji-backup')
ALIYUN_ENDPOINT = os.getenv('ALIYUN_ENDPOINT', '')
ALIYUN_UPLOAD_ENABLED = bool(ALIYUN_ACCESS_KEY_ID and ALIYUN_ACCESS_KEY_SECRET)

# 备份配置
DELETE_AFTER_UPLOAD = False  # 默认不删除本地文件（安全起见）
BACKUP_RETENTION_DAYS = 30  # 本地备份保留天数
AUTO_CLEAR_DEVICE = False  # 默认不自动清空设备（安全起见）

# 日志配置
LOG_LEVEL = logging.INFO
LOG_MAX_SIZE = 100 * 1024 * 1024  # 100MB
LOG_BACKUP_COUNT = 5

# MQTT配置
MQTT_ENABLED = False  # 默认关闭MQTT
MQTT_HOST = os.getenv('MQTT_HOST', 'localhost')
MQTT_PORT = int(os.getenv('MQTT_PORT', '1883'))
MQTT_USERNAME = os.getenv('MQTT_USERNAME', '')
MQTT_PASSWORD = os.getenv('MQTT_PASSWORD', '')

# 设备识别配置
DJI_VENDOR_IDS = ['2ca3']  # DJI Technology Co., Ltd.
DJI_KEYWORDS = ['dji', 'DJI', 'mavic', 'phantom', 'inspire', 'osmo']

# 并发配置
MAX_CONCURRENT_DEVICES = 3  # 降低并发数以提高稳定性
MAX_CONCURRENT_UPLOADS = 2

def get_current_config():
    """获取当前安全配置"""
    return {
        'delete_after_upload': DELETE_AFTER_UPLOAD,
        'wait_for_upload': True,
        'max_retries': QINIU_MAX_RETRIES,
        'retry_delay': QINIU_RETRY_DELAY,
        'upload_timeout': QINIU_UPLOAD_TIMEOUT,
        'auto_clear_device': AUTO_CLEAR_DEVICE,
        'security': SECURITY_CONFIG
    }

def validate_config():
    """验证配置的安全性"""
    errors = []
    
    # 检查必要的安全设置
    if SECURITY_CONFIG['enable_shell_commands']:
        errors.append("警告: shell命令已启用，存在安全风险")
    
    if DELETE_AFTER_UPLOAD and not (QINIU_UPLOAD_ENABLED or ALIYUN_UPLOAD_ENABLED):
        errors.append("错误: 启用了删除本地文件但未配置云存储")
    
    if AUTO_CLEAR_DEVICE:
        errors.append("警告: 自动清空设备已启用，请确认这是预期行为")
    
    # 检查云存储配置
    if CLOUD_UPLOAD_ENABLED:
        if CLOUD_STORAGE_PROVIDER == 'qiniu' and not QINIU_UPLOAD_ENABLED:
            errors.append("错误: 七牛云配置不完整")
        elif CLOUD_STORAGE_PROVIDER == 'aliyun' and not ALIYUN_UPLOAD_ENABLED:
            errors.append("错误: 阿里云配置不完整")
    
    return errors

def get_safe_mount_point(device_path):
    """生成安全的挂载点路径"""
    import hashlib
    import time
    
    # 验证设备路径
    if not device_path.startswith('/dev/'):
        return None
    
    # 生成安全的挂载点
    device_hash = hashlib.md5(device_path.encode()).hexdigest()[:8]
    timestamp = int(time.time())
    mount_point = f"{SECURITY_CONFIG['allowed_mount_base']}/dji_device_{device_hash}_{timestamp}"
    
    return mount_point

def get_safe_backup_dir(device_serial, user_home):
    """生成安全的备份目录路径"""
    import re
    
    # 清理设备序列号
    safe_serial = re.sub(r'[^a-zA-Z0-9_-]', '_', device_serial)
    
    # 选择安全的备份基础路径
    for base_path in SECURITY_CONFIG['allowed_backup_bases']:
        if user_home.startswith(base_path):
            backup_dir = os.path.join(user_home, f"dji_backup_{safe_serial}")
            return backup_dir
    
    # 默认使用第一个允许的路径
    backup_dir = os.path.join(SECURITY_CONFIG['allowed_backup_bases'][0], f"dji_backup_{safe_serial}")
    return backup_dir

if __name__ == "__main__":
    # 配置验证
    errors = validate_config()
    if errors:
        print("配置验证发现问题:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("配置验证通过")
    
    print(f"\n当前配置:")
    print(f"  云存储提供商: {CLOUD_STORAGE_PROVIDER}")
    print(f"  云上传状态: {'启用' if CLOUD_UPLOAD_ENABLED else '禁用'}")
    print(f"  删除本地文件: {'是' if DELETE_AFTER_UPLOAD else '否'}")
    print(f"  自动清空设备: {'是' if AUTO_CLEAR_DEVICE else '否'}")
    print(f"  最大并发设备: {MAX_CONCURRENT_DEVICES}")
    print(f"  Shell命令: {'启用' if SECURITY_CONFIG['enable_shell_commands'] else '禁用'}")
