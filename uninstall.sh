#!/bin/bash

# DJI设备监控服务卸载脚本

set -e

echo "=== DJI设备监控服务卸载程序 ==="
echo ""

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

echo "1. 检查服务状态..."

# 检查服务是否存在
if systemctl list-units --full -all | grep -Fq "dji-monitor.service"; then
    echo "发现DJI监控服务，准备停止..."

    # 停止服务
    echo "正在停止服务..."
    if systemctl is-active --quiet dji-monitor.service; then
        systemctl stop dji-monitor.service || echo "警告: 停止服务失败"
        sleep 2
    else
        echo "服务已经停止"
    fi
else
    echo "未发现DJI监控服务"
fi

echo "2. 禁用服务..."

# 禁用服务
if systemctl is-enabled --quiet dji-monitor.service 2>/dev/null; then
    echo "正在禁用服务..."
    systemctl disable dji-monitor.service 2>/dev/null || echo "警告: 禁用服务失败"
else
    echo "服务未启用或不存在"
fi

echo "3. 删除服务文件..."

# 删除systemd服务文件
if [ -f "/etc/systemd/system/dji-monitor.service" ]; then
    echo "删除systemd服务文件..."
    rm -f /etc/systemd/system/dji-monitor.service || echo "警告: 删除服务文件失败"
else
    echo "systemd服务文件不存在"
fi

# 重新加载systemd配置
echo "重新加载systemd配置..."
systemctl daemon-reload || echo "警告: 重新加载systemd配置失败"

echo "4. 安全清理挂载点..."

# 首先检查并卸载任何DJI设备挂载点
echo "检查DJI设备挂载点..."
if [ -f "/tmp/dji_mounts.log" ]; then
    while IFS=':' read -r device mount_point; do
        if [ -n "$mount_point" ] && mountpoint -q "$mount_point" 2>/dev/null; then
            echo "发现活动挂载点: $mount_point，尝试卸载..."
            umount "$mount_point" 2>/dev/null || umount -f "$mount_point" 2>/dev/null || echo "警告: 无法卸载 $mount_point"
        fi
    done < /tmp/dji_mounts.log
fi

# 检查/media下的DJI挂载点
for mount_point in /media/dji_device_*; do
    if [ -d "$mount_point" ] && mountpoint -q "$mount_point" 2>/dev/null; then
        echo "发现活动挂载点: $mount_point，尝试卸载..."
        umount "$mount_point" 2>/dev/null || umount -f "$mount_point" 2>/dev/null || echo "警告: 无法卸载 $mount_point"
    fi
done

echo "5. 删除程序文件..."

# 删除服务目录
if [ -d "/opt/dji_monitor" ]; then
    echo "删除服务目录..."
    rm -rf /opt/dji_monitor || echo "警告: 删除服务目录失败"
else
    echo "服务目录不存在"
fi

echo "6. 清理日志文件..."

# 删除日志文件
if [ -f "/var/log/dji_monitor.log" ]; then
    echo "删除日志文件..."
    rm -f /var/log/dji_monitor.log || echo "警告: 删除日志文件失败"
else
    echo "日志文件不存在"
fi

echo "7. 清理临时文件..."

# 清理临时挂载记录
if [ -f "/tmp/dji_mounts.log" ]; then
    echo "删除挂载记录文件..."
    rm -f /tmp/dji_mounts.log || echo "警告: 删除挂载记录失败"
fi

# 清理可能残留的空挂载点目录
echo "清理残留的挂载点目录..."
find /media -name "dji_device_*" -type d -empty -delete 2>/dev/null || true

echo ""
echo "✅ DJI设备监控服务卸载完成!"
echo ""
echo "注意事项:"
echo "  - Python依赖包(pyudev, qiniu, oss2, paho-mqtt)和系统工具未被删除"
echo "  - 如需完全清理Python依赖，可手动执行:"
echo "    sudo pip3 uninstall pyudev qiniu oss2 paho-mqtt"
echo "  - 如果之前遇到sudo权限问题，建议重启系统"
echo "  - 如果磁盘仍然不可用，请检查:"
echo "    df -h  # 检查磁盘使用情况"
echo "    mount  # 检查挂载状态"
echo "    sudo fsck /dev/sdX  # 检查文件系统（请替换sdX为实际设备）"
echo ""
echo "=== 卸载完成 ==="

