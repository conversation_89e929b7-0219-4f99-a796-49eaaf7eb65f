#!/usr/bin/env python3

"""
DJI设备状态上报接口
用于在设备连接、拔除和文件上传完成时上报状态信息
"""

import os
import sys
import json
import logging
from datetime import datetime
from typing import Optional, Dict, Any


class DeviceStatusReporter:
    """DJI设备状态上报器"""
    
    def __init__(self, log_file="/var/log/dji_monitor.log"):
        self.log_file = log_file
        self.setup_logging()
        self.logger.info("设备状态上报器初始化完成")
    
    def setup_logging(self):
        """设置日志"""
        log_dir = os.path.dirname(self.log_file)
        if log_dir and not os.path.exists(log_dir):
            os.makedirs(log_dir, exist_ok=True)
            
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler(sys.stdout)
            ]
        )
        self.logger = logging.getLogger(__name__)
    
    def get_device_info(self, device_path: str, device_serial: Optional[str] = None, usb_info: Optional[Dict] = None) -> Dict[str, Any]:
        """获取设备信息"""
        device_info = {
            'timestamp': datetime.now().isoformat(),
            'device_path': device_path,
            'device_id': os.path.basename(device_path),
            'device_serial': device_serial or 'unknown',
            'usb_info': usb_info or {},
            'hostname': os.uname().nodename if hasattr(os, 'uname') else 'unknown'
        }
        return device_info
    
    def report_device_connected(self, device_path: str, device_serial: Optional[str] = None, 
                               usb_info: Optional[Dict] = None, device_id: Optional[str] = None) -> bool:
        """上报设备连接事件"""
        try:
            device_info = self.get_device_info(device_path, device_serial, usb_info)
            device_info['event_type'] = 'device_connected'
            device_info['status'] = 'connected'
            
            self._log_report('设备连接', device_info, device_id)
            self._print_report_summary('设备连接', device_info, device_id)
            
            return True
            
        except Exception as e:
            error_msg = f"上报设备连接事件失败: {e}"
            if device_id:
                self.logger.error(f"[{device_id}] {error_msg}")
            else:
                self.logger.error(error_msg)
            return False
    
    def report_device_disconnected(self, device_path: str, device_serial: Optional[str] = None, 
                                  usb_info: Optional[Dict] = None, device_id: Optional[str] = None) -> bool:
        """上报设备断开事件"""
        try:
            device_info = self.get_device_info(device_path, device_serial, usb_info)
            device_info['event_type'] = 'device_disconnected'
            device_info['status'] = 'disconnected'
            
            self._log_report('设备断开', device_info, device_id)
            self._print_report_summary('设备断开', device_info, device_id)
            
            return True
            
        except Exception as e:
            error_msg = f"上报设备断开事件失败: {e}"
            if device_id:
                self.logger.error(f"[{device_id}] {error_msg}")
            else:
                self.logger.error(error_msg)
            return False
    
    def report_upload_completed(self, device_path: str, device_serial: Optional[str] = None, 
                               usb_info: Optional[Dict] = None, upload_stats: Optional[Dict] = None, 
                               device_id: Optional[str] = None) -> bool:
        """上报文件上传完成事件"""
        try:
            device_info = self.get_device_info(device_path, device_serial, usb_info)
            device_info['event_type'] = 'upload_completed'
            device_info['status'] = 'upload_success'
            device_info['upload_stats'] = upload_stats or {}
            
            self._log_report('文件上传完成', device_info, device_id)
            self._print_report_summary('文件上传完成', device_info, device_id)
            
            return True
            
        except Exception as e:
            error_msg = f"上报文件上传完成事件失败: {e}"
            if device_id:
                self.logger.error(f"[{device_id}] {error_msg}")
            else:
                self.logger.error(error_msg)
            return False
    
    def _log_report(self, event_name: str, device_info: Dict, device_id: Optional[str] = None):
        """记录上报信息到日志"""
        try:
            log_prefix = f"[{device_id}] " if device_id else ""
            self.logger.info(f"{log_prefix}=== {event_name}上报信息 ===")
            self.logger.info(f"{log_prefix}设备路径: {device_info.get('device_path', 'unknown')}")
            self.logger.info(f"{log_prefix}设备序列号: {device_info.get('device_serial', 'unknown')}")
            self.logger.info(f"{log_prefix}USB编号: {device_info.get('usb_info', {}).get('usb_port', 'unknown')}")
            self.logger.info(f"{log_prefix}事件类型: {device_info.get('event_type', 'unknown')}")
            self.logger.info(f"{log_prefix}状态: {device_info.get('status', 'unknown')}")
            self.logger.info(f"{log_prefix}时间戳: {device_info.get('timestamp', 'unknown')}")
            
            if 'upload_stats' in device_info:
                upload_stats = device_info['upload_stats']
                self.logger.info(f"{log_prefix}上传统计:")
                self.logger.info(f"{log_prefix}  总文件数: {upload_stats.get('total_files', 0)}")
                self.logger.info(f"{log_prefix}  成功上传: {upload_stats.get('uploaded_files', 0)}")
                self.logger.info(f"{log_prefix}  失败文件: {upload_stats.get('failed_files', 0)}")
                self.logger.info(f"{log_prefix}  总大小: {upload_stats.get('total_size', 0)} bytes")
                self.logger.info(f"{log_prefix}  已上传: {upload_stats.get('uploaded_size', 0)} bytes")
                
        except Exception as e:
            self.logger.error(f"记录上报信息失败: {e}")
    
    def _print_report_summary(self, event_name: str, device_info: Dict, device_id: Optional[str] = None):
        """打印上报信息摘要"""
        try:
            prefix = f"[{device_id}] " if device_id else ""
            print(f"{prefix}📊 {event_name}上报:")
            print(f"{prefix}  设备序列号: {device_info.get('device_serial', 'unknown')}")
            print(f"{prefix}  USB编号: {device_info.get('usb_info', {}).get('usb_port', 'unknown')}")
            print(f"{prefix}  状态: {device_info.get('status', 'unknown')}")
            print(f"{prefix}  时间: {device_info.get('timestamp', 'unknown')}")
            
            if 'upload_stats' in device_info:
                upload_stats = device_info['upload_stats']
                print(f"{prefix}  上传统计: {upload_stats.get('uploaded_files', 0)}/{upload_stats.get('total_files', 0)} 文件")
                
        except Exception as e:
            self.logger.error(f"打印上报信息摘要失败: {e}")


# 全局上报器实例
_reporter_instance = None

def get_reporter() -> DeviceStatusReporter:
    """获取全局上报器实例"""
    global _reporter_instance
    if _reporter_instance is None:
        if __name__ == "__main__":
            _reporter_instance = DeviceStatusReporter(log_file="./test_device_reporter.log")
        else:
            _reporter_instance = DeviceStatusReporter()
    return _reporter_instance

def report_device_connected(device_path: str, device_serial: Optional[str] = None, 
                           usb_info: Optional[Dict] = None, device_id: Optional[str] = None) -> bool:
    """上报设备连接事件（便捷函数）"""
    return get_reporter().report_device_connected(device_path, device_serial, usb_info, device_id)

def report_device_disconnected(device_path: str, device_serial: Optional[str] = None, 
                              usb_info: Optional[Dict] = None, device_id: Optional[str] = None) -> bool:
    """上报设备断开事件（便捷函数）"""
    return get_reporter().report_device_disconnected(device_path, device_serial, usb_info, device_id)

def report_upload_completed(device_path: str, device_serial: Optional[str] = None, 
                           usb_info: Optional[Dict] = None, upload_stats: Optional[Dict] = None, 
                           device_id: Optional[str] = None) -> bool:
    """上报文件上传完成事件（便捷函数）"""
    return get_reporter().report_upload_completed(device_path, device_serial, usb_info, upload_stats, device_id)


if __name__ == "__main__":
    """测试代码"""
    print("=== DJI设备状态上报接口测试 ===")
    
    reporter = DeviceStatusReporter(log_file="./test_device_reporter.log")
    
    test_device_path = "/dev/sdb1"
    test_device_serial = "DJI_MAVIC_3_PRO_123456"
    test_usb_info = {
        'usb_port': '1-2.3',
        'vendor_id': '2ca3',
        'product_id': '001f'
    }
    test_device_id = "sdb1"
    
    print("\n1. 测试设备连接上报:")
    reporter.report_device_connected(test_device_path, test_device_serial, test_usb_info, test_device_id)
    
    print("\n2. 测试上传完成上报:")
    test_upload_stats = {
        'total_files': 150,
        'uploaded_files': 150,
        'failed_files': 0,
        'total_size': 1024000000,
        'uploaded_size': 1024000000
    }
    reporter.report_upload_completed(test_device_path, test_device_serial, test_usb_info, test_upload_stats, test_device_id)
    
    print("\n3. 测试设备断开上报:")
    reporter.report_device_disconnected(test_device_path, test_device_serial, test_usb_info, test_device_id)
    
    print("\n=== 测试完成 ===")