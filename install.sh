#!/bin/bash

# DJI设备监控服务安装脚本

set -e

echo "=== DJI设备监控服务安装程序（多云存储版） ==="
echo ""

# 检查权限
if [ "$EUID" -ne 0 ]; then
    echo "错误: 请以root权限运行此脚本"
    echo "使用: sudo $0"
    exit 1
fi

echo "1. 检查系统环境..."

# 检查是否为Ubuntu/Debian系统
if ! command -v apt-get &> /dev/null; then
    echo "错误: 此脚本仅支持Ubuntu/Debian系统"
    exit 1
fi

# 检查磁盘空间
available_space=$(df / | awk 'NR==2 {print $4}')
if [ "$available_space" -lt 1048576 ]; then  # 1GB
    echo "警告: 根分区可用空间不足1GB，可能导致安装失败"
    read -p "是否继续安装？(y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo "2. 安装系统依赖..."

# 更新包列表
echo "正在更新包列表..."
if ! apt-get update; then
    echo "错误: 更新包列表失败"
    exit 1
fi

# 安装必要的系统工具
echo "正在安装系统工具..."
if ! apt-get install -y udev usbutils lsof python3 python3-pip; then
    echo "错误: 安装系统工具失败"
    exit 1
fi

echo "3. 安装Python依赖..."

# 检查pip3是否可用
if ! command -v pip3 &> /dev/null; then
    echo "错误: pip3 未正确安装"
    exit 1
fi

# 安装Python依赖包
echo "正在安装Python依赖包..."
if ! pip3 install pyudev qiniu oss2 paho-mqtt -i https://pypi.tuna.tsinghua.edu.cn/simple/; then
    echo "错误: 安装Python依赖包失败"
    exit 1
fi

echo "4. 创建服务目录..."

# 创建服务目录
echo "正在创建服务目录..."
if ! mkdir -p /opt/dji_monitor; then
    echo "错误: 创建服务目录失败"
    exit 1
fi

echo "5. 复制服务文件..."

# 检查必要文件是否存在
required_files=("dji_monitor.py" "dji-monitor.service")
for file in "${required_files[@]}"; do
    if [ ! -f "$file" ]; then
        echo "错误: 必要文件 $file 不存在"
        exit 1
    fi
done

# 复制服务脚本
echo "正在复制服务脚本..."
cp dji_monitor.py /opt/dji_monitor/ || { echo "错误: 复制 dji_monitor.py 失败"; exit 1; }

# 复制可选文件
optional_files=("cloud_storage_config.py" "aliyun_oss_uploader.py" "device_status_reporter.py" "mqtt_client.py")
for file in "${optional_files[@]}"; do
    if [ -f "$file" ]; then
        echo "复制 $file..."
        cp "$file" /opt/dji_monitor/ || echo "警告: 复制 $file 失败"
    else
        echo "警告: 可选文件 $file 不存在，跳过"
    fi
done

# 保持向后兼容，如果存在旧配置文件也复制
if [ -f "qiniu_config.py" ]; then
    echo "复制旧版配置文件 qiniu_config.py..."
    cp qiniu_config.py /opt/dji_monitor/
fi

# 设置文件权限
echo "设置文件权限..."
chmod +x /opt/dji_monitor/dji_monitor.py
for file in /opt/dji_monitor/*.py; do
    if [ -f "$file" ]; then
        chmod +x "$file"
    fi
done

# 复制systemd服务文件
echo "复制systemd服务文件..."
if ! cp dji-monitor.service /etc/systemd/system/; then
    echo "错误: 复制systemd服务文件失败"
    exit 1
fi

echo "6. 配置systemd服务..."

# 重新加载systemd配置
echo "重新加载systemd配置..."
if ! systemctl daemon-reload; then
    echo "错误: 重新加载systemd配置失败"
    exit 1
fi

# 启用服务（开机自启动）
echo "启用服务开机自启动..."
if ! systemctl enable dji-monitor.service; then
    echo "错误: 启用服务失败"
    exit 1
fi

echo "7. 启动服务..."

# 启动服务
echo "正在启动服务..."
if ! systemctl start dji-monitor.service; then
    echo "错误: 启动服务失败"
    echo "请检查日志: sudo journalctl -u dji-monitor -n 20"
    exit 1
fi

echo "8. 检查服务状态..."

# 等待服务启动
echo "等待服务启动..."
sleep 3

# 检查服务状态
echo "检查服务状态..."
if systemctl is-active --quiet dji-monitor.service; then
    echo "✅ DJI设备监控服务安装成功并正在运行!"
    echo ""
    echo "服务管理命令:"
    echo "  查看状态: sudo systemctl status dji-monitor"
    echo "  查看日志: sudo journalctl -u dji-monitor -f"
    echo "  查看专用日志: sudo tail -f /var/log/dji_monitor.log"
    echo "  重启服务: sudo systemctl restart dji-monitor"
    echo "  停止服务: sudo systemctl stop dji-monitor"
    echo ""
    echo "现在可以连接DJI设备测试自动挂载功能!"
    echo ""
    echo "注意事项:"
    echo "  - 请确保以sudo权限运行服务相关命令"
    echo "  - 如遇到权限问题，请检查sudo配置"
    echo "  - 建议定期检查磁盘空间，避免存储满载"
else
    echo "❌ 服务启动失败，请检查日志:"
    echo "  sudo journalctl -u dji-monitor -n 20"
    echo "  sudo systemctl status dji-monitor"
    exit 1
fi

echo ""
echo "=== 安装完成 ==="

