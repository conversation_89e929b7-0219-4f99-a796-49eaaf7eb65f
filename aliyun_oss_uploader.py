# -*- coding: utf-8 -*-
"""
阿里云OSS上传器
提供文件上传、重试机制、进度监控等功能
"""

import os
import time
import logging
import threading
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor
from threading import Lock

# 阿里云OSS相关导入
try:
    import oss2
    from oss2 import Auth, Bucket, exceptions
    from oss2.models import PartInfo
    ALIYUN_OSS_AVAILABLE = True
except ImportError:
    ALIYUN_OSS_AVAILABLE = False
    print("警告: 阿里云OSS SDK未安装，OSS上传功能将被禁用")
    print("请运行: pip install oss2")

class AliyunOSSUploader:
    """阿里云OSS上传器"""
    
    def __init__(self, config, logger=None):
        """
        初始化OSS上传器
        
        Args:
            config: OSS配置字典
            logger: 日志记录器
        """
        self.config = config
        self.logger = logger or logging.getLogger(__name__)
        self.upload_lock = Lock()
        
        # 检查OSS SDK可用性
        if not ALIYUN_OSS_AVAILABLE:
            self.enabled = False
            self.logger.warning("阿里云OSS SDK不可用，上传功能已禁用")
            return
        
        # 验证配置
        self._validate_config()
        
        # 初始化OSS客户端
        self._init_oss_client()
        
        # 上传统计
        self.upload_stats = {
            'total_files': 0,
            'uploaded_files': 0,
            'failed_files': 0,
            'total_size': 0,
            'uploaded_size': 0
        }
        
        self.enabled = True
        self.logger.info(f"阿里云OSS上传器初始化成功，目标存储桶: {self.config['bucket_name']}")
    
    def _validate_config(self):
        """验证OSS配置"""
        required_fields = ['access_key_id', 'access_key_secret', 'region', 'bucket_name']
        for field in required_fields:
            if not self.config.get(field):
                raise ValueError(f"缺少必要的OSS配置项: {field}")
    
    def _init_oss_client(self):
        """初始化OSS客户端"""
        try:
            # 创建认证对象
            self.auth = Auth(
                self.config['access_key_id'],
                self.config['access_key_secret']
            )
            
            # 构建endpoint
            if self.config.get('endpoint'):
                endpoint = self.config['endpoint']
            else:
                # 根据region自动构建endpoint
                region = self.config['region']
                if self.config.get('use_ssl', True):
                    endpoint = f"https://oss-{region}.aliyuncs.com"
                else:
                    endpoint = f"http://oss-{region}.aliyuncs.com"
            
            # 创建Bucket对象
            self.bucket = Bucket(
                self.auth,
                endpoint,
                self.config['bucket_name'],
                connect_timeout=self.config.get('connect_timeout', 60)
            )
            
            # 测试连接
            self._test_connection()
            
        except Exception as e:
            self.logger.error(f"初始化OSS客户端失败: {e}")
            raise
    
    def _test_connection(self):
        """测试OSS连接"""
        try:
            # 尝试获取存储桶信息
            bucket_info = self.bucket.get_bucket_info()
            self.logger.info(f"OSS连接测试成功，存储桶: {bucket_info.name}")
        except exceptions.NoSuchBucket:
            self.logger.error(f"存储桶不存在: {self.config['bucket_name']}")
            raise
        except exceptions.AccessDenied:
            self.logger.error("OSS访问被拒绝，请检查访问密钥和权限")
            raise
        except Exception as e:
            self.logger.warning(f"OSS连接测试失败: {e}")
            # 不抛出异常，允许继续运行
    
    def _generate_object_key(self, local_file_path, device_serial, base_dir):
        """生成OSS对象键（路径）"""
        try:
            # 计算相对路径
            rel_path = os.path.relpath(local_file_path, base_dir)
            
            # 生成日期字符串
            date_str = datetime.now().strftime('%Y%m%d')
            
            # 构建对象键
            if self.config.get('preserve_path', True):
                # 保持原始目录结构
                object_key = f"{device_serial}/{date_str}/{rel_path}"
            else:
                # 只保留文件名
                filename = os.path.basename(local_file_path)
                object_key = f"{device_serial}/{date_str}/{filename}"
            
            # 添加前缀
            if self.config.get('file_prefix'):
                object_key = f"{self.config['file_prefix']}/{object_key}"
            
            # 添加时间戳
            if self.config.get('use_timestamp', True):
                timestamp = int(time.time())
                name, ext = os.path.splitext(object_key)
                object_key = f"{name}_{timestamp}{ext}"
            
            # 规范化路径分隔符
            object_key = object_key.replace('\\', '/')
            
            return object_key
            
        except Exception as e:
            self.logger.error(f"生成对象键失败: {e}")
            # 备用方案
            filename = os.path.basename(local_file_path)
            timestamp = int(time.time())
            return f"{device_serial}/{timestamp}_{filename}"
    
    def _upload_file(self, local_file_path, object_key):
        """上传单个文件到OSS"""
        try:
            file_size = os.path.getsize(local_file_path)
            
            # 根据文件大小选择上传方式
            multipart_threshold = self.config.get('multipart_threshold', 100 * 1024 * 1024)  # 100MB
            
            if file_size > multipart_threshold:
                # 分片上传
                return self._multipart_upload(local_file_path, object_key, file_size)
            else:
                # 简单上传
                return self._simple_upload(local_file_path, object_key, file_size)
                
        except Exception as e:
            self.logger.error(f"上传文件失败: {local_file_path} -> {object_key}, 错误: {e}")
            return False
    
    def _simple_upload(self, local_file_path, object_key, file_size):
        """简单上传"""
        try:
            with open(local_file_path, 'rb') as f:
                result = self.bucket.put_object(object_key, f)
            
            if result.status == 200:
                self.logger.debug(f"简单上传成功: {object_key}")
                return True
            else:
                self.logger.error(f"简单上传失败: {object_key}, 状态码: {result.status}")
                return False
                
        except Exception as e:
            self.logger.error(f"简单上传异常: {object_key}, 错误: {e}")
            return False
    
    def _multipart_upload(self, local_file_path, object_key, file_size):
        """分片上传"""
        try:
            # 初始化分片上传
            upload_id = self.bucket.init_multipart_upload(object_key).upload_id
            
            # 计算分片
            chunk_size = self.config.get('chunk_size', 4 * 1024 * 1024)  # 4MB
            parts = []
            part_number = 1
            
            with open(local_file_path, 'rb') as f:
                while True:
                    data = f.read(chunk_size)
                    if not data:
                        break
                    
                    # 上传分片
                    result = self.bucket.upload_part(object_key, upload_id, part_number, data)
                    parts.append(PartInfo(part_number, result.etag))
                    part_number += 1
            
            # 完成分片上传
            result = self.bucket.complete_multipart_upload(object_key, upload_id, parts)
            
            if result.status == 200:
                self.logger.debug(f"分片上传成功: {object_key}")
                return True
            else:
                self.logger.error(f"分片上传失败: {object_key}, 状态码: {result.status}")
                return False
                
        except Exception as e:
            self.logger.error(f"分片上传异常: {object_key}, 错误: {e}")
            # 尝试取消分片上传
            try:
                self.bucket.abort_multipart_upload(object_key, upload_id)
            except:
                pass
            return False
    
    def upload_file_with_retry(self, local_file_path, object_key):
        """带重试机制的文件上传"""
        max_retries = self.config.get('max_retries', 3)
        retry_delay = self.config.get('retry_delay', 5)
        
        for attempt in range(max_retries + 1):
            try:
                if self._upload_file(local_file_path, object_key):
                    return True
                
                if attempt < max_retries:
                    self.logger.info(f"上传重试 {attempt + 1}/{max_retries}: {object_key}")
                    time.sleep(retry_delay)
                
            except Exception as e:
                self.logger.error(f"上传尝试 {attempt + 1} 异常: {object_key}, 错误: {e}")
                if attempt < max_retries:
                    time.sleep(retry_delay)
        
        self.logger.error(f"上传失败，已达到最大重试次数: {object_key}")
        return False
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        if size_bytes == 0:
            return "0 B"
        size_names = ["B", "KB", "MB", "GB", "TB"]
        i = 0
        while size_bytes >= 1024 and i < len(size_names) - 1:
            size_bytes /= 1024.0
            i += 1
        return f"{size_bytes:.1f} {size_names[i]}"
    
    def upload_directory(self, local_dir, device_serial, device_id=None, delete_after_upload=False):
        """上传整个目录到OSS"""
        if not self.enabled:
            if device_id:
                self.thread_safe_log('warning', "阿里云OSS上传功能未启用", device_id)
            else:
                self.logger.warning("阿里云OSS上传功能未启用")
            return False

        try:
            if device_id:
                self.thread_safe_log('info', f"开始上传到阿里云OSS: {local_dir}", device_id)
            else:
                self.logger.info(f"开始上传到阿里云OSS: {local_dir}")

            # 统计需要上传的文件
            upload_files = []
            total_size = 0

            for root, _, files in os.walk(local_dir):
                for file in files:
                    local_file_path = os.path.join(root, file)
                    object_key = self._generate_object_key(local_file_path, device_serial, local_dir)

                    file_size = os.path.getsize(local_file_path)
                    upload_files.append({
                        'local_path': local_file_path,
                        'object_key': object_key,
                        'size': file_size
                    })
                    total_size += file_size

            if not upload_files:
                if device_id:
                    self.thread_safe_log('warning', "没有找到需要上传的文件", device_id)
                else:
                    self.logger.warning("没有找到需要上传的文件")
                return True

            if device_id:
                self.thread_safe_log('info', f"准备上传 {len(upload_files)} 个文件，总大小: {self.format_size(total_size)}", device_id)
            else:
                self.logger.info(f"准备上传 {len(upload_files)} 个文件，总大小: {self.format_size(total_size)}")

            # 更新统计信息
            with self.upload_lock:
                self.upload_stats['total_files'] += len(upload_files)
                self.upload_stats['total_size'] += total_size

            # 逐个上传文件
            success_count = 0
            failed_count = 0
            uploaded_size = 0
            successfully_uploaded_files = []  # 记录成功上传的文件路径

            for i, file_info in enumerate(upload_files, 1):
                try:
                    if device_id:
                        self.thread_safe_log('info', f"上传文件 {i}/{len(upload_files)}: {file_info['object_key']}", device_id)
                    else:
                        self.logger.info(f"上传文件 {i}/{len(upload_files)}: {file_info['object_key']}")

                    # 执行上传
                    if self.upload_file_with_retry(file_info['local_path'], file_info['object_key']):
                        success_count += 1
                        uploaded_size += file_info['size']
                        successfully_uploaded_files.append(file_info['local_path'])

                        if device_id:
                            self.thread_safe_log('debug', f"上传成功: {file_info['object_key']}", device_id)
                        else:
                            self.logger.debug(f"上传成功: {file_info['object_key']}")
                    else:
                        failed_count += 1
                        if device_id:
                            self.thread_safe_log('error', f"上传失败: {file_info['object_key']}", device_id)
                        else:
                            self.logger.error(f"上传失败: {file_info['object_key']}")

                except Exception as e:
                    failed_count += 1
                    if device_id:
                        self.thread_safe_log('error', f"上传异常: {file_info['object_key']}, 错误: {e}", device_id)
                    else:
                        self.logger.error(f"上传异常: {file_info['object_key']}, 错误: {e}")

            # 更新统计信息
            with self.upload_lock:
                self.upload_stats['uploaded_files'] += success_count
                self.upload_stats['failed_files'] += failed_count
                self.upload_stats['uploaded_size'] += uploaded_size

            # 记录上传结果
            if device_id:
                self.thread_safe_log('info', f"阿里云OSS上传完成: 成功 {success_count}/{len(upload_files)} 个文件", device_id)
                self.thread_safe_log('info', f"上传大小: {self.format_size(uploaded_size)}/{self.format_size(total_size)}", device_id)
            else:
                self.logger.info(f"阿里云OSS上传完成: 成功 {success_count}/{len(upload_files)} 个文件")
                self.logger.info(f"上传大小: {self.format_size(uploaded_size)}/{self.format_size(total_size)}")

            # 如果启用了上传后删除功能，删除成功上传的文件
            if delete_after_upload and successfully_uploaded_files:
                self._delete_uploaded_files(successfully_uploaded_files, local_dir, device_id)

            return success_count > 0

        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"阿里云OSS上传异常: {e}", device_id)
            else:
                self.logger.error(f"阿里云OSS上传异常: {e}")
            import traceback
            self.logger.error(f"异常详情: {traceback.format_exc()}")
            return False

    def _delete_uploaded_files(self, successfully_uploaded_files, local_dir, device_id=None):
        """删除成功上传的本地文件"""
        if device_id:
            self.thread_safe_log('info', f"开始删除已成功上传的本地文件，共 {len(successfully_uploaded_files)} 个", device_id)
        else:
            self.logger.info(f"开始删除已成功上传的本地文件，共 {len(successfully_uploaded_files)} 个")

        deleted_count = 0
        deleted_size = 0

        for file_path in successfully_uploaded_files:
            try:
                file_size = os.path.getsize(file_path)
                os.remove(file_path)
                deleted_count += 1
                deleted_size += file_size

                if device_id:
                    self.thread_safe_log('debug', f"删除本地文件: {file_path}", device_id)
                else:
                    self.logger.debug(f"删除本地文件: {file_path}")

            except Exception as e:
                if device_id:
                    self.thread_safe_log('error', f"删除本地文件失败: {file_path}, 错误: {e}", device_id)
                else:
                    self.logger.error(f"删除本地文件失败: {file_path}, 错误: {e}")

        if device_id:
            self.thread_safe_log('info', f"本地文件删除完成: 成功删除 {deleted_count}/{len(successfully_uploaded_files)} 个文件", device_id)
            self.thread_safe_log('info', f"释放空间: {self.format_size(deleted_size)}", device_id)
        else:
            self.logger.info(f"本地文件删除完成: 成功删除 {deleted_count}/{len(successfully_uploaded_files)} 个文件")
            self.logger.info(f"释放空间: {self.format_size(deleted_size)}")

        # 尝试删除空目录
        try:
            self._cleanup_empty_directories(local_dir, device_id)
        except Exception as e:
            if device_id:
                self.thread_safe_log('warning', f"清理空目录失败: {e}", device_id)
            else:
                self.logger.warning(f"清理空目录失败: {e}")

    def _cleanup_empty_directories(self, root_dir, device_id=None):
        """清理空目录（从叶子节点开始）"""
        try:
            if device_id:
                self.thread_safe_log('info', f"开始清理空目录: {root_dir}", device_id)
            else:
                self.logger.info(f"开始清理空目录: {root_dir}")

            # 从底层开始删除空目录
            for root, _, _ in os.walk(root_dir, topdown=False):
                # 跳过根目录
                if root == root_dir:
                    continue

                try:
                    # 如果目录为空，删除它
                    if not os.listdir(root):
                        os.rmdir(root)
                        if device_id:
                            self.thread_safe_log('debug', f"删除空目录: {root}", device_id)
                        else:
                            self.logger.debug(f"删除空目录: {root}")
                except OSError as e:
                    # 目录不为空或其他错误，跳过
                    if device_id:
                        self.thread_safe_log('debug', f"无法删除目录: {root}, 错误: {e}", device_id)
                    else:
                        self.logger.debug(f"无法删除目录: {root}, 错误: {e}")

        except Exception as e:
            if device_id:
                self.thread_safe_log('error', f"清理空目录异常: {e}", device_id)
            else:
                self.logger.error(f"清理空目录异常: {e}")

    def get_upload_stats(self):
        """获取上传统计信息"""
        with self.upload_lock:
            return self.upload_stats.copy()

    def reset_upload_stats(self):
        """重置上传统计信息"""
        with self.upload_lock:
            self.upload_stats = {
                'total_files': 0,
                'uploaded_files': 0,
                'failed_files': 0,
                'total_size': 0,
                'uploaded_size': 0
            }

    def thread_safe_log(self, level, message, device_id=None):
        """线程安全的日志记录"""
        try:
            thread_name = threading.current_thread().name
            if device_id:
                log_message = f"[{thread_name}][{device_id}] {message}"
            else:
                log_message = f"[{thread_name}] {message}"

            if level == 'info':
                self.logger.info(log_message)
            elif level == 'warning':
                self.logger.warning(log_message)
            elif level == 'error':
                self.logger.error(log_message)
            elif level == 'debug':
                self.logger.debug(log_message)

        except Exception as e:
            # 如果日志记录失败，使用print作为备用
            print(f"日志记录失败: {e}, 原消息: {message}")
