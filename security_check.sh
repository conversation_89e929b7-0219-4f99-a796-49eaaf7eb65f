#!/bin/bash

# DJI监控服务安全检查脚本

echo "=== DJI监控服务安全检查 ==="
echo ""

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "警告: 建议以root权限运行此脚本以获得完整检查结果"
    echo "使用: sudo $0"
    echo ""
fi

echo "1. 检查服务配置安全性..."

if [ -f "/etc/systemd/system/dji-monitor.service" ]; then
    echo "检查systemd服务配置:"
    
    # 检查用户配置
    if grep -q "User=root" /etc/systemd/system/dji-monitor.service; then
        echo "⚠️  服务以root用户运行 - 高风险"
    fi
    
    # 检查安全设置
    if grep -q "NoNewPrivileges=false" /etc/systemd/system/dji-monitor.service; then
        echo "🔴 NoNewPrivileges=false - 极高风险"
    elif grep -q "NoNewPrivileges=true" /etc/systemd/system/dji-monitor.service; then
        echo "✅ NoNewPrivileges=true - 安全"
    fi
    
    if grep -q "ProtectSystem=false" /etc/systemd/system/dji-monitor.service; then
        echo "🔴 ProtectSystem=false - 极高风险"
    elif grep -q "ProtectSystem=strict" /etc/systemd/system/dji-monitor.service; then
        echo "✅ ProtectSystem=strict - 安全"
    fi
    
    if grep -q "ProtectHome=false" /etc/systemd/system/dji-monitor.service; then
        echo "🔴 ProtectHome=false - 高风险"
    elif grep -q "ProtectHome=true" /etc/systemd/system/dji-monitor.service; then
        echo "✅ ProtectHome=true - 安全"
    fi
else
    echo "systemd服务文件不存在"
fi
echo ""

echo "2. 检查文件权限..."

# 检查关键文件权限
files_to_check=(
    "/opt/dji_monitor/dji_monitor.py"
    "/etc/systemd/system/dji-monitor.service"
    "/usr/bin/sudo"
)

for file in "${files_to_check[@]}"; do
    if [ -f "$file" ]; then
        perms=$(ls -la "$file")
        echo "$file: $perms"
        
        # 检查sudo权限
        if [ "$file" = "/usr/bin/sudo" ]; then
            if [ -u "$file" ]; then
                echo "✅ sudo具有setuid权限"
            else
                echo "🔴 sudo缺少setuid权限 - 这可能导致权限问题!"
            fi
        fi
    else
        echo "$file: 文件不存在"
    fi
done
echo ""

echo "3. 检查挂载点安全性..."

# 检查当前DJI挂载点
echo "当前DJI挂载点:"
mount | grep -i dji || echo "无DJI挂载点"

# 检查/media目录权限
if [ -d "/media" ]; then
    echo "/media目录权限: $(ls -ld /media)"
    
    # 检查是否有异常的DJI挂载点
    find /media -name "dji_device_*" -type d 2>/dev/null | while read dir; do
        echo "发现DJI挂载点: $dir"
        if mountpoint -q "$dir" 2>/dev/null; then
            echo "  状态: 已挂载"
        else
            echo "  状态: 未挂载（可能是残留目录）"
        fi
    done
fi
echo ""

echo "4. 检查进程状态..."

# 检查DJI相关进程
echo "DJI相关进程:"
ps aux | grep -i dji | grep -v grep || echo "无DJI相关进程"

# 检查服务状态
if systemctl list-units --full -all | grep -Fq "dji-monitor.service"; then
    echo ""
    echo "DJI监控服务状态:"
    systemctl status dji-monitor.service --no-pager -l | head -10
fi
echo ""

echo "5. 检查日志中的安全问题..."

if [ -f "/var/log/dji_monitor.log" ]; then
    echo "检查最近的错误日志:"
    tail -20 /var/log/dji_monitor.log | grep -i "error\|fail\|exception" | head -5 || echo "无明显错误"
fi

if [ "$EUID" -eq 0 ]; then
    echo ""
    echo "检查系统日志中的相关错误:"
    journalctl -u dji-monitor --since "1 hour ago" --priority=err --no-pager | head -5 || echo "无系统错误日志"
fi
echo ""

echo "6. 磁盘安全检查..."

# 检查磁盘使用情况
echo "磁盘使用情况:"
df -h | head -10

# 检查是否有满载的分区
df -h | awk 'NR>1 {gsub(/%/, "", $5); if ($5 > 95) print "⚠️  分区 " $6 " 使用率过高: " $5 "%"}'

# 检查临时文件
if [ -f "/tmp/dji_mounts.log" ]; then
    echo ""
    echo "临时挂载记录:"
    cat /tmp/dji_mounts.log | head -5
fi
echo ""

echo "7. 安全建议..."
echo ""
echo "🔧 立即修复建议:"
echo "1. 如果发现sudo权限问题，执行: sudo chmod u+s /usr/bin/sudo"
echo "2. 停止服务: sudo systemctl stop dji-monitor"
echo "3. 更新服务配置以增强安全性"
echo "4. 清理残留的挂载点: sudo umount /media/dji_device_* 2>/dev/null"
echo "5. 检查并修复文件系统: sudo fsck -f /dev/sdX"
echo ""
echo "🛡️  长期安全建议:"
echo "1. 定期检查服务日志"
echo "2. 监控磁盘使用情况"
echo "3. 限制服务权限"
echo "4. 定期备份重要数据"
echo "5. 考虑使用非root用户运行服务"
echo ""

echo "=== 安全检查完成 ==="
