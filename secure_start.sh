#!/bin/bash

# DJI监控服务安全启动脚本

echo "🛡️  DJI监控服务安全启动脚本"
echo "================================"

# 检查是否以root权限运行
if [ "$EUID" -ne 0 ]; then
    echo "❌ 错误: 此脚本需要root权限"
    echo "请使用: sudo $0"
    exit 1
fi

echo "✅ Root权限检查通过"

# 1. 停止现有服务
echo ""
echo "🔄 停止现有DJI监控服务..."
systemctl stop dji-monitor 2>/dev/null || true
systemctl disable dji-monitor 2>/dev/null || true

# 2. 安全检查
echo ""
echo "🔍 执行安全检查..."

# 检查sudo权限
if [ ! -u "/usr/bin/sudo" ]; then
    echo "⚠️  修复sudo权限..."
    chmod u+s /usr/bin/sudo
    echo "✅ sudo权限已修复"
fi

# 检查并清理危险的挂载点
echo "🧹 清理残留挂载点..."
for mount_point in $(mount | grep "/media/dji_device_" | awk '{print $3}'); do
    echo "  卸载: $mount_point"
    umount "$mount_point" 2>/dev/null || umount -f "$mount_point" 2>/dev/null || true
    rmdir "$mount_point" 2>/dev/null || true
done

# 3. 验证配置文件
echo ""
echo "📋 验证配置文件..."

if [ -f "secure_config.py" ]; then
    echo "✅ 安全配置文件存在"
    python3 secure_config.py
else
    echo "❌ 安全配置文件不存在，请先创建"
    exit 1
fi

# 4. 验证Python依赖
echo ""
echo "📦 检查Python依赖..."

required_packages=("pyudev")
missing_packages=()

for package in "${required_packages[@]}"; do
    if ! python3 -c "import $package" 2>/dev/null; then
        missing_packages+=("$package")
    fi
done

if [ ${#missing_packages[@]} -gt 0 ]; then
    echo "⚠️  缺少Python包: ${missing_packages[*]}"
    echo "安装命令: pip3 install ${missing_packages[*]}"
    read -p "是否现在安装? (y/N): " install_deps
    if [[ $install_deps =~ ^[Yy]$ ]]; then
        pip3 install "${missing_packages[@]}"
    else
        echo "❌ 请先安装缺少的依赖"
        exit 1
    fi
fi

# 5. 创建安全的目录结构
echo ""
echo "📁 创建安全目录结构..."

# 创建日志目录
mkdir -p /var/log/dji_monitor
chmod 755 /var/log/dji_monitor

# 创建备份目录
mkdir -p /opt/dji_backup
chmod 755 /opt/dji_backup

# 创建服务目录
mkdir -p /opt/dji_monitor
chmod 755 /opt/dji_monitor

# 6. 复制文件到安全位置
echo ""
echo "📋 部署服务文件..."

# 复制Python文件
cp dji_monitor.py /opt/dji_monitor/
cp secure_config.py /opt/dji_monitor/
chmod 644 /opt/dji_monitor/*.py

# 复制systemd服务文件
cp dji-monitor.service /etc/systemd/system/
chmod 644 /etc/systemd/system/dji-monitor.service

# 7. 验证systemd服务配置
echo ""
echo "🔧 验证systemd服务配置..."

if systemd-analyze verify /etc/systemd/system/dji-monitor.service; then
    echo "✅ systemd服务配置验证通过"
else
    echo "❌ systemd服务配置有问题"
    exit 1
fi

# 8. 重新加载systemd
echo ""
echo "🔄 重新加载systemd..."
systemctl daemon-reload

# 9. 安全性最终检查
echo ""
echo "🛡️  最终安全检查..."

# 检查服务文件权限
service_perms=$(stat -c "%a" /etc/systemd/system/dji-monitor.service)
if [ "$service_perms" != "644" ]; then
    echo "⚠️  修复服务文件权限..."
    chmod 644 /etc/systemd/system/dji-monitor.service
fi

# 检查Python文件权限
python_perms=$(stat -c "%a" /opt/dji_monitor/dji_monitor.py)
if [ "$python_perms" != "644" ]; then
    echo "⚠️  修复Python文件权限..."
    chmod 644 /opt/dji_monitor/dji_monitor.py
fi

echo "✅ 权限检查完成"

# 10. 询问是否启动服务
echo ""
echo "🚀 准备启动服务..."
echo ""
echo "⚠️  重要提醒:"
echo "   - 服务已配置为安全模式"
echo "   - 默认不会自动删除本地文件"
echo "   - 默认不会自动清空设备"
echo "   - 已禁用危险的shell命令"
echo "   - 限制了并发数量以提高稳定性"
echo ""

read -p "是否现在启动DJI监控服务? (y/N): " start_service

if [[ $start_service =~ ^[Yy]$ ]]; then
    echo ""
    echo "🔄 启动服务..."
    
    systemctl enable dji-monitor
    systemctl start dji-monitor
    
    # 等待服务启动
    sleep 3
    
    # 检查服务状态
    if systemctl is-active --quiet dji-monitor; then
        echo "✅ DJI监控服务启动成功!"
        echo ""
        echo "📊 服务状态:"
        systemctl status dji-monitor --no-pager -l | head -10
        echo ""
        echo "📝 查看日志: journalctl -u dji-monitor -f"
        echo "📝 查看应用日志: tail -f /var/log/dji_monitor.log"
    else
        echo "❌ 服务启动失败"
        echo ""
        echo "🔍 错误信息:"
        journalctl -u dji-monitor --no-pager -l | tail -10
        exit 1
    fi
else
    echo ""
    echo "ℹ️  服务未启动，您可以稍后手动启动:"
    echo "   sudo systemctl start dji-monitor"
    echo "   sudo systemctl enable dji-monitor"
fi

echo ""
echo "🎉 安全部署完成!"
echo ""
echo "📚 重要文件位置:"
echo "   - 服务文件: /etc/systemd/system/dji-monitor.service"
echo "   - Python代码: /opt/dji_monitor/dji_monitor.py"
echo "   - 安全配置: /opt/dji_monitor/secure_config.py"
echo "   - 日志文件: /var/log/dji_monitor.log"
echo "   - 备份目录: /opt/dji_backup/"
echo ""
echo "🔧 管理命令:"
echo "   - 启动服务: sudo systemctl start dji-monitor"
echo "   - 停止服务: sudo systemctl stop dji-monitor"
echo "   - 查看状态: sudo systemctl status dji-monitor"
echo "   - 查看日志: sudo journalctl -u dji-monitor -f"
echo "   - 安全检查: sudo ./security_check.sh"
